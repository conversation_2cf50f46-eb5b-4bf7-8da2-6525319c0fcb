import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class BrightdataService {
  private readonly logger = new Logger(BrightdataService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async triggerDataset(datasetId: string, payload: any[]): Promise<any> {
    try {
      const baseUrl = this.configService.get<string>('BRIGHTDATA_BASE_URL');
      const apiKey = this.configService.get<string>('BRIGHTDATA_API_KEY');

      if (!baseUrl || !apiKey) {
        throw new HttpException(
          'BrightData configuration is missing',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const url = `${baseUrl}?dataset_id=${datasetId}&include_errors=true`;
      
      this.logger.log(`Triggering BrightData dataset: ${datasetId}`);
      
      const response = await firstValueFrom(
        this.httpService.post(url, payload, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        }),
      );

      this.logger.log(`BrightData response received for dataset: ${datasetId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`BrightData API error: ${error.message}`);
      throw new HttpException(
        'Failed to fetch data from BrightData',
        HttpStatus.BAD_GATEWAY,
      );
    }
  }
}