import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BrightdataService } from '../../brightdata/brightdata.service';
import { LinkedInUrlDto } from '../../brightdata/dto';
import { PeopleProfile } from './entities/people-profile.entity';

@Injectable()
export class PeopleProfileCollectService {
  private readonly logger = new Logger(PeopleProfileCollectService.name);

  constructor(
    private readonly brightdataService: BrightdataService,
    private readonly configService: ConfigService,
    @InjectRepository(PeopleProfile)
    private readonly peopleProfileRepository: Repository<PeopleProfile>,
  ) {}

  async collectProfiles(linkedInUrlDto: LinkedInUrlDto) {
    try {
      const datasetId = this.configService.get<string>('PEOPLE_PROFILE_COLLECT_DATASET_ID');

      if (!datasetId) {
        throw new Error('People Profile Collect dataset ID is not configured');
      }

      // Transform URLs for BrightData API
      const payload = linkedInUrlDto.urls.map(url => ({ url }));

      this.logger.log(`Collecting profiles for ${payload.length} URLs`);

      // Call BrightData API to trigger data collection
      const brightDataResponse = await this.brightdataService.triggerDataset(
        datasetId,
        payload,
      );

      this.logger.log(`BrightData response structure: ${JSON.stringify(brightDataResponse, null, 2)}`);

      // Check if we got a snapshot_id (async operation)
      if (brightDataResponse?.snapshot_id) {
        this.logger.log(`Received snapshot_id: ${brightDataResponse.snapshot_id}, waiting for data collection to complete...`);

        // Wait for data collection to complete and get the actual profile data
        const profilesData = await this.waitForDataAndRetrieve(brightDataResponse.snapshot_id);

        // Save profiles to database
        const savedProfiles = await this.saveProfilesToDatabase(profilesData);

        this.logger.log(`Successfully processed ${savedProfiles.length} profiles`);

        return {
          success: true,
          message: `Successfully collected ${savedProfiles.length} profiles`,
          data: profilesData,
          saved_count: savedProfiles.length,
          snapshot_id: brightDataResponse.snapshot_id,
        };
      } else {
        // Handle direct response (if any)
        const profilesData = this.extractProfilesFromResponse(brightDataResponse);
        const savedProfiles = await this.saveProfilesToDatabase(profilesData);

        this.logger.log(`Successfully processed ${savedProfiles.length} profiles`);

        return {
          success: true,
          message: `Successfully collected ${savedProfiles.length} profiles`,
          data: profilesData,
          saved_count: savedProfiles.length,
        };
      }
    } catch (error) {
      this.logger.error(`Error collecting profiles: ${error.message}`);
      throw error;
    }
  }

  /**
   * Wait for BrightData collection to complete and retrieve the actual profile data
   */
  private async waitForDataAndRetrieve(snapshotId: string, maxWaitTime: number = 300000, pollInterval: number = 5000): Promise<any[]> {
    const startTime = Date.now();

    this.logger.log(`Starting to poll for snapshot completion: ${snapshotId}`);

    while (Date.now() - startTime < maxWaitTime) {
      try {
        // Check progress
        const progress = await this.brightdataService.monitorProgress(snapshotId);
        this.logger.debug(`Progress status: ${progress.status}`);

        if (progress.status === 'completed' || progress.status === 'ready') {
          this.logger.log(`Data collection completed for snapshot: ${snapshotId}`);

          // Download the actual data
          const snapshotData = await this.brightdataService.downloadSnapshot(snapshotId);

          // Extract profiles from the downloaded data
          return this.extractProfilesFromResponse(snapshotData);
        } else if (progress.status === 'failed' || progress.status === 'error') {
          this.logger.error(`Data collection failed for snapshot: ${snapshotId}`);
          throw new Error(`BrightData collection failed with status: ${progress.status}`);
        } else {
          // Still running, wait and poll again
          this.logger.debug(`Data collection still in progress (${progress.status}), waiting ${pollInterval}ms...`);
          await new Promise(resolve => setTimeout(resolve, pollInterval));
        }
      } catch (error) {
        this.logger.error(`Error while polling for snapshot ${snapshotId}: ${error.message}`);

        // If it's a timeout or network error, continue polling
        if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
          this.logger.warn('Network error while polling, retrying...');
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          continue;
        }

        // For other errors, throw immediately
        throw error;
      }
    }

    // Timeout reached
    this.logger.warn(`Timeout reached while waiting for snapshot ${snapshotId} to complete`);
    throw new Error(`Timeout: Data collection did not complete within ${maxWaitTime / 1000} seconds`);
  }

  /**
   * Extract profiles array from BrightData response
   * BrightData can return different response formats:
   * - Direct array: [profile1, profile2, ...]
   * - Object with data property: { data: [profile1, profile2, ...] }
   * - Object with results property: { results: [profile1, profile2, ...] }
   * - Other nested structures
   */
  private extractProfilesFromResponse(response: any): any[] {
    this.logger.log(`Extracting profiles from response type: ${typeof response}`);

    // If response is null or undefined
    if (!response) {
      this.logger.warn('BrightData response is null or undefined');
      return [];
    }

    // If response is already an array
    if (Array.isArray(response)) {
      this.logger.log(`Response is array with ${response.length} items`);
      return response;
    }

    // If response is an object, try to find the profiles array
    if (typeof response === 'object') {
      // Common property names that might contain the profiles array
      const possibleArrayKeys = ['data', 'results', 'profiles', 'items', 'records'];

      for (const key of possibleArrayKeys) {
        if (response[key] && Array.isArray(response[key])) {
          this.logger.log(`Found profiles array in response.${key} with ${response[key].length} items`);
          return response[key];
        }
      }

      // If no array found in common keys, log the structure and return empty array
      this.logger.warn(`No profiles array found in response. Available keys: ${Object.keys(response).join(', ')}`);
      return [];
    }

    // If response is neither array nor object
    this.logger.warn(`Unexpected response type: ${typeof response}`);
    return [];
  }

  private async saveProfilesToDatabase(profiles: any[]): Promise<PeopleProfile[]> {
    const savedProfiles: PeopleProfile[] = [];

    if (!Array.isArray(profiles)) {
      this.logger.error('Profiles parameter is not an array');
      return savedProfiles;
    }

    if (profiles.length === 0) {
      this.logger.warn('No profiles to save to database');
      return savedProfiles;
    }

    this.logger.log(`Attempting to save ${profiles.length} profiles to database`);

    for (const profileData of profiles) {
      try {
        if (!profileData || typeof profileData !== 'object') {
          this.logger.warn('Skipping invalid profile data:', profileData);
          continue;
        }

        // Check if profile already exists
        const existingProfile = await this.peopleProfileRepository.findOne({
          where: { linkedin_num_id: profileData.linkedin_num_id },
        });

        if (existingProfile) {
          // Update existing profile
          Object.assign(existingProfile, profileData);
          const updated = await this.peopleProfileRepository.save(existingProfile);
          savedProfiles.push(updated);
          this.logger.log(`Updated existing profile: ${profileData.linkedin_num_id}`);
        } else {
          // Create new profile
          const newProfile = this.peopleProfileRepository.create(profileData);
          const saved = await this.peopleProfileRepository.save(newProfile);
          if (Array.isArray(saved)) {
            savedProfiles.push(...saved);
          } else {
            savedProfiles.push(saved);
          }
          this.logger.log(`Saved new profile: ${profileData.linkedin_num_id}`);
        }
      } catch (error) {
        this.logger.error(`Error saving profile ${profileData?.linkedin_num_id || 'unknown'}: ${error.message}`);
      }
    }

    return savedProfiles;
  }

  async getAllProfiles() {
    return this.peopleProfileRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  async getProfileById(id: string) {
    return this.peopleProfileRepository.findOne({ where: { id } });
  }

  async getProfileByLinkedInId(linkedinId: string) {
    return this.peopleProfileRepository.findOne({
      where: { linkedin_id: linkedinId },
    });
  }

  async getSnapshotStatus(snapshotId: string) {
    try {
      const progress = await this.brightdataService.monitorProgress(snapshotId);

      return {
        success: true,
        snapshot_id: snapshotId,
        status: progress.status,
        dataset_id: progress.dataset_id,
        message: `Snapshot status: ${progress.status}`,
      };
    } catch (error) {
      this.logger.error(`Error getting snapshot status ${snapshotId}: ${error.message}`);
      throw error;
    }
  }

  async getSnapshotData(snapshotId: string) {
    try {
      // First check if the snapshot is ready
      const progress = await this.brightdataService.monitorProgress(snapshotId);

      if (progress.status !== 'completed' && progress.status !== 'ready') {
        return {
          success: false,
          snapshot_id: snapshotId,
          status: progress.status,
          message: `Snapshot is not ready yet. Current status: ${progress.status}`,
        };
      }

      // Download the snapshot data
      const snapshotData = await this.brightdataService.downloadSnapshot(snapshotId);

      // Extract profiles from the downloaded data
      const profilesData = this.extractProfilesFromResponse(snapshotData);

      // Save profiles to database
      const savedProfiles = await this.saveProfilesToDatabase(profilesData);

      this.logger.log(`Successfully processed ${savedProfiles.length} profiles from snapshot ${snapshotId}`);

      return {
        success: true,
        snapshot_id: snapshotId,
        status: progress.status,
        message: `Successfully retrieved ${profilesData.length} profiles`,
        data: profilesData,
        saved_count: savedProfiles.length,
      };
    } catch (error) {
      this.logger.error(`Error getting snapshot data ${snapshotId}: ${error.message}`);
      throw error;
    }
  }
}
