import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BrightdataService } from '../../brightdata/brightdata.service';
import { LinkedInUrlDto } from '../../brightdata/dto';
import { PeopleProfile } from './entities/people-profile.entity';

@Injectable()
export class PeopleProfileCollectService {
  private readonly logger = new Logger(PeopleProfileCollectService.name);

  constructor(
    private readonly brightdataService: BrightdataService,
    private readonly configService: ConfigService,
    @InjectRepository(PeopleProfile)
    private readonly peopleProfileRepository: Repository<PeopleProfile>,
  ) {}

  async collectProfiles(linkedInUrlDto: LinkedInUrlDto) {
    try {
      const datasetId = this.configService.get<string>('PEOPLE_PROFILE_COLLECT_DATASET_ID');
      
      if (!datasetId) {
        throw new Error('People Profile Collect dataset ID is not configured');
      }

      // Transform URLs for BrightData API
      const payload = linkedInUrlDto.urls.map(url => ({ url }));

      this.logger.log(`Collecting profiles for ${payload.length} URLs`);

      // Call BrightData API
      const brightDataResponse = await this.brightdataService.triggerDataset(
        datasetId,
        payload,
      );

      // Save profiles to database
      const savedProfiles = await this.saveProfilesToDatabase(brightDataResponse);

      this.logger.log(`Successfully processed ${savedProfiles.length} profiles`);

      return {
        success: true,
        message: `Successfully collected ${savedProfiles.length} profiles`,
        data: brightDataResponse,
        saved_count: savedProfiles.length,
      };
    } catch (error) {
      this.logger.error(`Error collecting profiles: ${error.message}`);
      throw error;
    }
  }

  private async saveProfilesToDatabase(profiles: any[]): Promise<PeopleProfile[]> {
    const savedProfiles: PeopleProfile[] = [];

    for (const profileData of profiles) {
      try {
        // Check if profile already exists
        const existingProfile = await this.peopleProfileRepository.findOne({
          where: { linkedin_num_id: profileData.linkedin_num_id },
        });

        if (existingProfile) {
          // Update existing profile
          Object.assign(existingProfile, profileData);
          const updated = await this.peopleProfileRepository.save(existingProfile);
          savedProfiles.push(updated);
          this.logger.log(`Updated existing profile: ${profileData.linkedin_num_id}`);
        } else {
          // Create new profile
          const newProfile = this.peopleProfileRepository.create(profileData);
          const saved = await this.peopleProfileRepository.save(newProfile);
          if (Array.isArray(saved)) {
            savedProfiles.push(...saved);
          } else {
            savedProfiles.push(saved);
          }
          this.logger.log(`Saved new profile: ${profileData.linkedin_num_id}`);
        }
      } catch (error) {
        this.logger.error(`Error saving profile ${profileData.linkedin_num_id}: ${error.message}`);
      }
    }

    return savedProfiles;
  }

  async getAllProfiles() {
    return this.peopleProfileRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  async getProfileById(id: string) {
    return this.peopleProfileRepository.findOne({ where: { id } });
  }

  async getProfileByLinkedInId(linkedinId: string) {
    return this.peopleProfileRepository.findOne({
      where: { linkedin_id: linkedinId },
    });
  }
}
