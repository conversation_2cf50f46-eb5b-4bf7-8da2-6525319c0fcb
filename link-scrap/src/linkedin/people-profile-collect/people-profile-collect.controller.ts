import { Controller, Post, Body, Get, Param, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { PeopleProfileCollectService } from './people-profile-collect.service';
import { LinkedInUrlDto } from '../../brightdata/dto';

@ApiTags('LinkedIn People Profile - Collect')
@Controller('linkedin/people-profile/collect')
export class PeopleProfileCollectController {
  constructor(
    private readonly peopleProfileCollectService: PeopleProfileCollectService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Collect LinkedIn profiles by URLs',
    description: 'Collect detailed LinkedIn profile information by providing profile URLs'
  })
  @ApiBody({ 
    type: LinkedInUrlDto,
    examples: {
      example1: {
        summary: 'Single URL example',
        value: {
          urls: ['https://www.linkedin.com/in/elad-moshe-05a90413/']
        }
      },
      example2: {
        summary: 'Multiple URLs example',
        value: {
          urls: [
            'https://www.linkedin.com/in/elad-moshe-05a90413/',
            'https://www.linkedin.com/in/jonathan-myrvik-3baa01109',
            'https://www.linkedin.com/in/aviv-tal-75b81/'
          ]
        }
      }
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Profiles collected successfully' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data' 
  })
  @ApiResponse({ 
    status: 502, 
    description: 'BrightData API error' 
  })
  async collectProfiles(@Body() linkedInUrlDto: LinkedInUrlDto) {
    return this.peopleProfileCollectService.collectProfiles(linkedInUrlDto);
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all collected profiles',
    description: 'Retrieve all LinkedIn profiles stored in the database'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Profiles retrieved successfully' 
  })
  async getAllProfiles() {
    return this.peopleProfileCollectService.getAllProfiles();
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get profile by ID',
    description: 'Retrieve a specific LinkedIn profile by its database ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Profile retrieved successfully' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Profile not found' 
  })
  async getProfileById(@Param('id') id: string) {
    return this.peopleProfileCollectService.getProfileById(id);
  }

  @Get('linkedin-id/:linkedinId')
  @ApiOperation({ 
    summary: 'Get profile by LinkedIn ID',
    description: 'Retrieve a specific LinkedIn profile by its LinkedIn ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Profile retrieved successfully' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Profile not found' 
  })
  async getProfileByLinkedInId(@Param('linkedinId') linkedinId: string) {
    return this.peopleProfileCollectService.getProfileByLinkedInId(linkedinId);
  }
}
