import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { PeopleProfileDiscoverService } from './people-profile-discover.service';
import { CreatePeopleProfileDiscoverDto } from './dto/create-people-profile-discover.dto';
import { UpdatePeopleProfileDiscoverDto } from './dto/update-people-profile-discover.dto';

@Controller('people-profile-discover')
export class PeopleProfileDiscoverController {
  constructor(private readonly peopleProfileDiscoverService: PeopleProfileDiscoverService) {}

  @Post()
  create(@Body() createPeopleProfileDiscoverDto: CreatePeopleProfileDiscoverDto) {
    return this.peopleProfileDiscoverService.create(createPeopleProfileDiscoverDto);
  }

  @Get()
  findAll() {
    return this.peopleProfileDiscoverService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.peopleProfileDiscoverService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updatePeopleProfileDiscoverDto: UpdatePeopleProfileDiscoverDto) {
    return this.peopleProfileDiscoverService.update(+id, updatePeopleProfileDiscoverDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.peopleProfileDiscoverService.remove(+id);
  }
}
