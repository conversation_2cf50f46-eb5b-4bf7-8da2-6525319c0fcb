import { Injectable } from '@nestjs/common';
import { CreatePeopleProfileDiscoverDto } from './dto/create-people-profile-discover.dto';
import { UpdatePeopleProfileDiscoverDto } from './dto/update-people-profile-discover.dto';

@Injectable()
export class PeopleProfileDiscoverService {
  create(createPeopleProfileDiscoverDto: CreatePeopleProfileDiscoverDto) {
    return 'This action adds a new peopleProfileDiscover';
  }

  findAll() {
    return `This action returns all peopleProfileDiscover`;
  }

  findOne(id: number) {
    return `This action returns a #${id} peopleProfileDiscover`;
  }

  update(id: number, updatePeopleProfileDiscoverDto: UpdatePeopleProfileDiscoverDto) {
    return `This action updates a #${id} peopleProfileDiscover`;
  }

  remove(id: number) {
    return `This action removes a #${id} peopleProfileDiscover`;
  }
}
