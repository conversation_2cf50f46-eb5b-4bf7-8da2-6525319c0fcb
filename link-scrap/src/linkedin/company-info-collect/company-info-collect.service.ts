import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BrightdataService } from '../../brightdata/brightdata.service';
import { CompanyUrlDto } from '../../brightdata/dto';
import { CompanyInfoEntity } from './entities';

@Injectable()
export class CompanyInfoCollectService {
  private readonly logger = new Logger(CompanyInfoCollectService.name);

  constructor(
    @InjectRepository(CompanyInfoEntity)
    private companyInfoRepository: Repository<CompanyInfoEntity>,
    private brightdataService: BrightdataService,
  ) {}

  async collectCompanyInfo(companyUrlDto: CompanyUrlDto) {
    this.logger.log('Starting company info collection for URLs: ' + JSON.stringify(companyUrlDto.urls));

    const datasetId = 'gd_l1vikfnt1wgvvqz95w'; // BrightData Company Information dataset ID
    
    try {
      const brightdataResponse = await this.brightdataService.triggerDataset(datasetId, companyUrlDto.urls);
      
      this.logger.log('BrightData response received for company info collection');

      const results: CompanyInfoEntity[] = [];
      
      // Process each URL result and save to database
      for (let i = 0; i < companyUrlDto.urls.length; i++) {
        const url = companyUrlDto.urls[i];
        const companyData = brightdataResponse.data?.[i];
        
        if (companyData) {
          const companyEntity = this.companyInfoRepository.create({
            company_id: companyData.company_id || this.extractCompanyId(url),
            name: companyData.name || 'Unknown',
            website: companyData.website,
            phone: companyData.phone,
            description: companyData.description,
            about: companyData.about,
            url: companyData.url || url,
            image_url: companyData.image_url,
            background_image_url: companyData.background_image_url,
            followers: companyData.followers,
            organization_type: companyData.organization_type,
            employees: companyData.employees,
            employees_range: companyData.employees_range,
            headquarters: companyData.headquarters,
            founded: companyData.founded,
            industries: companyData.industries,
            headquarters_geolocation: companyData.headquarters_geolocation,
            specialities: companyData.specialities,
            locations: companyData.locations,
            social_media: companyData.social_media,
            employees_insights: companyData.employees_insights,
            funding: companyData.funding,
            acquisitions: companyData.acquisitions,
            similar_companies: companyData.similar_companies,
            affiliated_pages: companyData.affiliated_pages,
            showcase_pages: companyData.showcase_pages,
            featured_groups: companyData.featured_groups,
            updates: companyData.updates,
            original_request_url: url,
            raw_data: companyData,
            data_source: 'brightdata',
            collection_status: 'completed',
            brightdata_input: JSON.stringify({ url }),
            collected_at: new Date(),
          });

          const savedEntity = await this.companyInfoRepository.save(companyEntity);
          results.push(savedEntity);
          
          this.logger.log(`Company info saved for company_id: ${savedEntity.company_id}`);
        } else {
          // Create entry for failed collection
          const failedEntity = this.companyInfoRepository.create({
            company_id: this.extractCompanyId(url),
            name: 'Failed Collection',
            url: url,
            original_request_url: url,
            data_source: 'brightdata',
            collection_status: 'failed',
            collection_error: 'No data received from BrightData',
            brightdata_input: JSON.stringify({ url }),
            collected_at: new Date(),
          });

          const savedEntity = await this.companyInfoRepository.save(failedEntity);
          results.push(savedEntity);
          
          this.logger.warn(`No data received for URL: ${url}`);
        }
      }

      return {
        success: true,
        message: `Successfully processed ${results.length} companies`,
        data: results,
        brightdata_response: brightdataResponse,
      };
      
    } catch (error) {
      this.logger.error('Error collecting company info: ', error);
      
      // Save failed attempts to database
      const failedResults: CompanyInfoEntity[] = [];
      for (const url of companyUrlDto.urls) {
        const failedEntity = this.companyInfoRepository.create({
          company_id: this.extractCompanyId(url),
          name: 'Failed Collection',
          url: url,
          original_request_url: url,
          data_source: 'brightdata',
          collection_status: 'error',
          collection_error: error.message,
          brightdata_input: JSON.stringify({ url }),
          collected_at: new Date(),
        });

        const savedEntity = await this.companyInfoRepository.save(failedEntity);
        failedResults.push(savedEntity);
      }
      
      return {
        success: false,
        message: 'Failed to collect company info',
        error: error.message,
        data: failedResults,
      };
    }
  }

  async findAll() {
    return this.companyInfoRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string) {
    return this.companyInfoRepository.findOne({
      where: { id },
    });
  }

  async findByCompanyId(companyId: string) {
    return this.companyInfoRepository.findOne({
      where: { company_id: companyId },
    });
  }

  async findByUrl(url: string) {
    return this.companyInfoRepository.find({
      where: { original_request_url: url },
      order: { createdAt: 'DESC' },
    });
  }

  async remove(id: string) {
    const result = await this.companyInfoRepository.delete(id);
    return result.affected ? result.affected > 0 : false;
  }

  private extractCompanyId(url: string): string {
    // Extract company identifier from LinkedIn URL
    const match = url.match(/\/company\/([^\/\?]+)/);
    if (match) {
      return match[1];
    }
    
    // Fallback to timestamp-based ID if extraction fails
    return `company_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
